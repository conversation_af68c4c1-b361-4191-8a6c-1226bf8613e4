# Configurable Header Template

This document explains how to use the new configurable header template in your TYPO3 DBI package.

## Overview

The configurable header template allows you to create pages with custom header links that can be configured directly in the TYPO3 backend. This is useful for creating landing pages or special pages that need different call-to-action buttons in the header.

## How to Use

### 1. Create a New Page

1. Create a new page in the TYPO3 backend
2. In the page properties, go to the "Appearance" tab
3. Select "Konfigurierbare Header-Links" as the backend layout

### 2. Configure Header Links

1. In the page properties, you'll see a new tab called "Header Links"
2. Click on this tab to configure your custom header links
3. Click "Add new" to create a new header link
4. For each link, you can configure:
   - **Link Title**: The text that will be displayed on the button
   - **Link**: The URL or page link (use the link wizard to select pages, external URLs, etc.)
   - **Icon Name** (optional): The name of an icon to display next to the text

### 3. Available Icons

You can use any of the existing icons from your icon set. Common icons include:
- `sold-immobilie` - For property estimation links
- `share` - For sharing or broker search links
- `link` - Generic link icon
- Leave empty for no icon

### 4. Responsive Behavior

- **Desktop**: All configured links will be displayed as buttons in the header
- **Mobile**: Only the first 2 links will be displayed as icon buttons
- **Fallback**: If no custom links are configured, the template will show the default "Immobilie einschätzen" and "Makler suchen" buttons

## Technical Details

### Files Created/Modified

1. **Backend Layout**: `Configuration/TsConfig/Page/pagelayout.tsconfig`
   - Added `dbiConfigurableHeader` backend layout

2. **Page Template**: `Resources/Private/Templates/Page/ConfigurableHeader.html`
   - New template that uses the configurable header layout

3. **Layout**: `Resources/Private/Layouts/ConfigurableHeader.html`
   - Layout that includes the configurable header partial

4. **Header Partial**: `Resources/Private/Partials/Header/ConfigurableHeader.html`
   - Contains the logic for rendering configurable header links

5. **TCA Configuration**: `Configuration/TCA/Overrides/pages.php`
   - Added `tx_dbi_header_links` field to pages

6. **FlexForm**: `Configuration/FlexForms/PageHeaderLinks.xml`
   - Defines the structure for configuring header links

7. **Data Processor**: `Classes/DataProcessing/HeaderLinksProcessor.php`
   - Processes FlexForm data for use in templates

8. **TypoScript**: `Configuration/TypoScript/page.typoscript`
   - Added template mapping and data processing configuration

## Customization

You can customize the appearance and behavior by:

1. **Modifying the CSS classes** in the header partial
2. **Adding new icon options** by updating the FlexForm configuration
3. **Changing the responsive behavior** by modifying the mobile/desktop conditions in the template
4. **Adding validation** by extending the HeaderLinksProcessor class

## Example Use Cases

- **Landing Pages**: Create special landing pages with specific call-to-action buttons
- **Campaign Pages**: Add campaign-specific links in the header
- **Regional Pages**: Include region-specific contact or service links
- **Product Pages**: Add product-specific action buttons

## Troubleshooting

If the header links are not showing:

1. Make sure you've selected the correct backend layout ("Konfigurierbare Header-Links")
2. Verify that you've added at least one link with both title and URL
3. Check that the TypoScript template is included
4. Clear all caches (Configuration > Clear All Caches)
