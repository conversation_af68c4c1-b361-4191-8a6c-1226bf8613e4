# Header Jump Links Template

This document explains how to use the new header jump links template in your TYPO3 DBI package.

## Overview

The header jump links template allows you to create pages with navigation links in the header that jump to specific container sections on the same page. This is perfect for one-page layouts, landing pages, or long-form content where users need quick navigation to different sections.

## How to Use

### 1. Create a New Page

1. Create a new page in the TYPO3 backend
2. In the page properties, go to the "Appearance" tab
3. Select "Konfigurierbare Header-Links" as the backend layout

### 2. Add Container Content Elements

1. Add container content elements to your page (e.g., `tx_dbi_container_one_column`, `tx_dbi_container_two_columns`, etc.)
2. Make sure each container has a **header/title** filled in
3. These headers will be used as the jump link text and anchor targets

### 3. Configure Jump Links

1. In the page properties, go to the "Header Links" tab
2. You'll see a list of all container components with headers on this page
3. Check the boxes next to the containers you want to include in the header navigation
4. The order will follow the container sorting on the page

### 4. How It Works

- **Automatic Detection**: The system automatically finds all container components with headers
- **Checkbox Selection**: You choose which containers to include in the navigation
- **Anchor Generation**: Anchor IDs are automatically generated from the header text
- **Smooth Scrolling**: Clicking a link smoothly scrolls to that section

## Features

### Desktop Navigation
- Jump links appear as text links in the header
- Clean, minimal design that doesn't interfere with branding
- Hover effects for better user experience

### Mobile Navigation
- Hamburger menu with dropdown for jump links
- Touch-friendly interface
- Automatically closes after selection

### Smooth Scrolling
- JavaScript-powered smooth scrolling
- Accounts for fixed header offset
- Works across all modern browsers

### Automatic Anchor IDs
- Generated from header text (e.g., "About Us" → "about-us")
- URL-safe characters only
- Consistent and predictable

## Technical Details

### Files Created/Modified

1. **Backend Layout**: `Configuration/TsConfig/Page/pagelayout.tsconfig`
   - Added `dbiConfigurableHeader` backend layout

2. **Page Template**: `Resources/Private/Templates/Page/ConfigurableHeader.html`
   - Uses the configurable header layout

3. **Layout**: `Resources/Private/Layouts/ConfigurableHeader.html`
   - Layout with configurable header partial

4. **Header Partial**: `Resources/Private/Partials/Header/ConfigurableHeader.html`
   - Contains jump link navigation logic

5. **TCA Configuration**: `Configuration/TCA/Overrides/pages.php`
   - Added `tx_dbi_header_links` field to pages

6. **FlexForm**: `Configuration/FlexForms/PageHeaderLinks.xml`
   - Custom backend interface for container selection

7. **Backend Classes**:
   - `Classes/Backend/HeaderLinksInfo.php` - Information display
   - `Classes/Backend/ContainerHeaderSelector.php` - Container selection interface

8. **Data Processors**:
   - `Classes/DataProcessing/HeaderLinksProcessor.php` - Processes selected containers
   - `Classes/DataProcessing/ContainerAnchorProcessor.php` - Adds anchor IDs to containers

9. **Container Templates**: Modified to support anchor IDs
   - `Resources/Private/Templates/Container/MainContainer.html`
   - `Resources/Private/Templates/Container/TwoColumnContainer.html`
   - `Resources/Private/Templates/Container/ThreeColumnContainer.html`
   - `Resources/Private/Templates/Container/FourColumnContainer.html`

10. **Headline Component**: `Resources/Private/Components/Headline/Headline.html`
    - Added support for anchor IDs

11. **JavaScript**: `Resources/Private/JavaScript/smooth-scroll.js`
    - Smooth scrolling functionality

12. **TypoScript**: Updated configuration for data processing

## Setup Instructions

### 1. Clear Caches
```bash
make typo3-clear-cache
```

### 2. Update Database Schema
```bash
docker compose -f compose.dev.yml exec --user www-data:www-data typo3 ./vendor/bin/typo3 database:updateschema
```

### 3. Test the Feature
1. Create a test page with the "Konfigurierbare Header-Links" backend layout
2. Add several container content elements with headers
3. Go to "Header Links" tab and select containers
4. View the page - you should see jump links in the header

## Example Workflow

1. **Create Page**: New page with "Konfigurierbare Header-Links" layout
2. **Add Containers**:
   - Container 1: Header "About Us"
   - Container 2: Header "Our Services"
   - Container 3: Header "Contact"
3. **Select Links**: Check boxes for containers you want in navigation
4. **Result**: Header shows "About Us | Our Services | Contact" as clickable jump links

## Troubleshooting

### No containers showing in Header Links tab
- Make sure containers have headers/titles filled in
- Check that you're using `tx_dbi_container_*` content types
- Clear caches and reload the page

### Jump links not working
- Check browser console for JavaScript errors
- Verify anchor IDs are being generated (view page source)
- Make sure smooth-scroll.js is loading

### Links not appearing in header
- Confirm you've selected the correct backend layout
- Check that containers are selected in Header Links tab
- Clear frontend caches

## Customization

### Styling
Modify the header partial to change:
- Link colors and hover effects
- Mobile menu appearance
- Spacing and typography

### Scroll Behavior
Edit `smooth-scroll.js` to adjust:
- Scroll speed and easing
- Header offset compensation
- Mobile behavior

### Container Detection
Extend `ContainerHeaderSelector.php` to:
- Include other content types
- Add filtering options
- Customize the selection interface
