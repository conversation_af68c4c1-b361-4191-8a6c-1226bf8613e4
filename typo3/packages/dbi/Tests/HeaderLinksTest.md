# Header Links Test Instructions

## Steps to Test the Header Links Feature

### 1. Clear All Caches
```bash
make typo3-clear-cache
```
Or if using Docker directly:
```bash
docker compose -f compose.dev.yml exec -it -u www-data typo3 ./vendor/bin/typo3 cache:flush
```

### 2. Update Database Schema
```bash
docker compose -f compose.dev.yml exec --user www-data:www-data typo3 ./vendor/bin/typo3 database:updateschema
```

### 3. Create Test Page
1. Go to TYPO3 Backend → Web → Page
2. Create a new page (or edit existing one)
3. In page properties → "Appearance" tab
4. Set "Backend Layout" to "Konfigurierbare Header-Links"
5. **Important**: You should now see a new tab called "Header Links"

### 4. Configure Header Links
1. Click on the "Header Links" tab
2. Click "Add new" to create a header link
3. Fill in:
   - **Link Title**: "Test Link"
   - **Link**: Select a page or enter URL
   - **Icon Name**: "sold-immobilie" (optional)
4. Save the page

### 5. View Frontend
- Visit the page on frontend
- You should see your custom header link instead of default buttons

## Troubleshooting

### If "Header Links" tab is not showing:
1. Make sure you selected "Konfigurierbare Header-Links" backend layout
2. Clear all caches
3. Update database schema
4. Check if the field exists in database:
   ```sql
   DESCRIBE pages;
   ```
   Should show `tx_dbi_header_links` field

### If links are not showing on frontend:
1. Check browser console for JavaScript errors
2. Verify the template is being used (check page source)
3. Clear frontend caches

### Database Check
```sql
SELECT uid, title, backend_layout, tx_dbi_header_links 
FROM pages 
WHERE backend_layout = 'pagets__dbiConfigurableHeader';
```

## Expected Behavior
- **Desktop**: Custom links show as buttons in header
- **Mobile**: First 2 links show as icon buttons
- **No links configured**: Shows default buttons as fallback
