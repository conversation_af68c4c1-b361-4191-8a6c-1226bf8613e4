# Header Links Debug Guide

## Current Status

The implementation has been updated to support **both DBI containers AND Mask elements**. Here's what should work now:

### ✅ **Supported Content Types**
- `tx_dbi_container_*` (DBI containers)
- `tx_dbi_stage_container` (DBI stage container)
- `mask_*` (All Mask elements with headers)

### ✅ **Updated Files for Mask Support**
- `ContainerHeaderSelector.php` - Now detects Mask elements
- `HeaderLinksProcessor.php` - Processes Mask containers
- `ContainerAnchorProcessor.php` - Adds anchor IDs to Mask elements
- Mask templates updated:
  - `ContentSnippet.html`
  - `Teaser.html`
  - `Text.html`
  - `Media.html`
  - `Personenbox.html`
- `Content/Snippet/Snippet.html` - Component supports anchor IDs
- `Headline/Headline.html` - Restored anchor ID support

## Debug Steps

### 1. **Check Page Type**
- Make sure your page has **doktype = 12** (Headerlinks page type)
- Not just the backend layout, but the actual page type

### 2. **Check Database**
```sql
-- Check if the field exists
DESCRIBE pages;

-- Check your page
SELECT uid, title, doktype, tx_dbi_header_links 
FROM pages 
WHERE uid = YOUR_PAGE_ID;
```

### 3. **Check Content Elements**
```sql
-- See what content elements are on your page
SELECT uid, header, CType, sorting 
FROM tt_content 
WHERE pid = YOUR_PAGE_ID 
AND deleted = 0 
AND header != ''
ORDER BY sorting;
```

### 4. **Debug Output**
The debug output you added should show:
- `{_all}` - All available data in the template
- `dump($containers)` - Container data from HeaderLinksProcessor

### 5. **Expected Behavior**

**In Backend:**
1. Create page with type "Headerlinks" (doktype 12)
2. Add Mask elements with headers (e.g., `mask_content_snippet`, `mask_teaser`)
3. Go to "Header Links" tab
4. Should see checkboxes for each Mask element with header

**On Frontend:**
1. Select some containers in backend
2. Save page
3. View frontend
4. Should see jump links in header
5. Clicking should scroll to sections

## Troubleshooting

### ❌ **No containers showing in backend**
- Check if content elements have headers filled in
- Verify CType starts with `tx_dbi_container_` or `mask_`
- Clear caches

### ❌ **Backend tab not showing**
- Verify page type is "Headerlinks" (doktype 12)
- Check database field exists
- Clear caches and update schema

### ❌ **Jump links not working**
- Check browser console for JavaScript errors
- Verify anchor IDs are in HTML source
- Check if smooth-scroll.js is loading

### ❌ **Anchor IDs not appearing**
- Verify containers are selected in backend
- Check if `ContainerAnchorProcessor` is running
- Look for `anchor_id` in debug output

## Manual Test

1. **Create test page:**
   - Type: "Headerlinks" (doktype 12)
   
2. **Add test content:**
   - Add a `mask_content_snippet` with header "About Us"
   - Add a `mask_teaser` with header "Our Services"
   - Add a `mask_text` with header "Contact"

3. **Configure links:**
   - Go to "Header Links" tab
   - Should see 3 checkboxes
   - Select all 3
   - Save

4. **Test frontend:**
   - Should see "About Us | Our Services | Contact" in header
   - Clicking should scroll to sections
   - Each section should have `id="about-us"` etc.

## Debug Commands

```bash
# Clear caches
make typo3-clear-cache

# Update database
docker compose -f compose.dev.yml exec --user www-data:www-data typo3 ./vendor/bin/typo3 database:updateschema

# Check logs
docker compose -f compose.dev.yml logs typo3
```

Let me know what you see in the debug output and I can help troubleshoot further!
