lib.favicons {
  link = IMG_RESOURCE
  link {
    stdWrap.prepend = TEXT
    stdWrap.prepend.char = 10
  }
}

config {
    pageTitleFirst = 1
    pageTitleSeparator = |
    pageTitleSeparator.noTrimWrap = | | |

    pageTitleProviders {
        dbi_center {
            provider = Mogic\Dbi\PageTitle\CenterPageTitleProvider
            before = record
        }
        dbi_fioimmoproxy {
            provider = Mogic\Dbi\PageTitle\FioImmoProxyPageTitleProvider
            before = record
        }
    }
}

lib.maskContentElement {
    partialRootPaths.0 < lib.contentElement.partialRootPaths.0
    layoutRootPaths.0 < lib.contentElement.layoutRootPaths.0

    settings < lib.contentElement.settings

    dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
        10 {
            special = directory
            special.value = 1
            as = pageTree
            levels = 10
            expandAll = 1
        }
    }

    variables {
        sellEstatePageUid = TEXT
        sellEstatePageUid {
            value = {$dbi.pids.sellEstate}
        }
        searchRequestPageUid = TEXT
        searchRequestPageUid {
            value = {$dbi.pids.searchRequest}
        }
        marketPriceEstimationPid = TEXT
        marketPriceEstimationPid {
          value = {$dbi.pids.marketPriceEstimation}
        }
        brokerSearchPid = TEXT
        brokerSearchPid {
          value = {$dbi.pids.brokerSearch}
        }
        exposePageUid = TEXT
        exposePageUid {
            value = {$dbi.pids.expose}
        }
    }
}

lib.contentElement {
    layoutRootPaths.0   = EXT:dbi/Resources/Private/Layouts/Mask/
    templateRootPaths {
        1277 = EXT:dbi/Resources/Private/Templates/
    }
}

lib.svgHandler = SVG
lib.svgHandler {
    renderMode = inline
    src.current = 1
    stdWrap.wrap = <span aria-hidden="true">|</span>
}

page = PAGE
page {
    shortcutIcon = {$dbi.theme.favicon_path}/favicon.ico
    typeNum = 0

    meta {
        viewport = width=device-width, initial-scale=1, shrink-to-fit=no

        X-UA-Compatible = IE=edge
        X-UA-Compatible.attribute = http-equiv

        content-language = de-de
        content-language.attribute = http-equiv

        msapplication-TileColor = {$dbi.theme.color}
        theme-color = {$dbi.theme.color}
    }

    headerData {
        10 = COA
        10 {
            1 < lib.favicons.link
            1.file = {$dbi.theme.favicon_path}/apple-touch-icon.png
            1.stdWrap.wrap = <link rel="apple-touch-icon" sizes="180x180" href="|">

            2 < lib.favicons.link
            2.file = {$dbi.theme.favicon_path}/favicon-32x32.png
            2.stdWrap.wrap = <link rel="icon" type="image/png" sizes="32x32" href="|">

            3 < lib.favicons.link
            3.file = {$dbi.theme.favicon_path}/favicon-16x16.png
            3.stdWrap.wrap = <link rel="icon" type="image/png" sizes="16x16" href="|">
        }
    }

    20 = FLUIDTEMPLATE
    20 {
        templateRootPaths.20 = EXT:dbi/Resources/Private/Templates/
        partialRootPaths.20  = EXT:dbi/Resources/Private/Partials/
        layoutRootPaths.20   = EXT:dbi/Resources/Private/Layouts/

        file.stdWrap.cObject = CASE
        file.stdWrap.cObject {
            key.field = backend_layout

            default = TEXT
            default.value = EXT:dbi/Resources/Private/Templates/Page/Default.html

            #this is on a center, which should be a mount point anyway
            # this template will not get used.
            pagets__dbiCenter = TEXT
            pagets__dbiCenter.value = EXT:dbi/Resources/Private/Templates/Page/Default.html

            pagets__dbiCenterTemplate = TEXT
            pagets__dbiCenterTemplate.value = EXT:dbi/Resources/Private/Templates/Page/Default.html

            pagets__dbiCookieBanner = TEXT
            pagets__dbiCookieBanner.value = EXT:dbi/Resources/Private/Templates/Page/CookieBanner.html

            pagets__dbiProxy = TEXT
            pagets__dbiProxy.value = EXT:dbi/Resources/Private/Templates/Page/Proxy.html

            pagets__dbiDemo = TEXT
            pagets__dbiDemo.value = EXT:dbi/Resources/Private/Templates/Page/Demo.html
        }

        dataProcessing {

            # Content
            20 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
            20 {
                table = tt_content
                orderBy = sorting
                where = colPos = 1
                as = stageContent
            }

            21 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
            21 {
                table = tt_content
                orderBy = sorting
                where = colPos = 0
                as = mainContent
            }

            # Menus
            30 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            30 {
                levels = 2
                includeSpacer = 1
                as = mainMenu
                titleField = nav_title // title
            }

            31 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            31 {
                special = list
                special.value.field = pages
                includeSpacer = 1
                expandAll = 1
                levels = 3
                entryLevel = 1
                as = subMainMenu
                titleField = nav_title // title
            }

            32 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            32 {
                special = directory
                special.value = {$dbi.pids.header_support_menu}
                as = headerSupportMenu
                levels = 1
                expandAll = 0
                includeSpacer = 0
                includeNotInMenu = 0
                titleField = nav_title // title
            }

            33 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            33 {
                special = directory
                special.value = {$dbi.pids.footer_menu}
                as = footerMenu
                expandAll = 1
                levels = 2
                includeSpacer = 0
                includeNotInMenu = 0
                titleField = nav_title // title
            }

            34 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            34 {
                special = directory
                special.value = {$dbi.pids.footer_support_menu}
                as = footerSupportMenu
                levels = 2
                expandAll = 0
                includeSpacer = 0
                includeNotInMenu = 0
                titleField = nav_title // title
            }

            50 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            50 {
                #files = {$dbi.images.allcenterawards}
                #references.data = {$dbi.images.allcenterawards}
                folders = 1:siegel
                as = awards
            }
        }

        #variables < lib.maskContentElement.variables
        variables {
            specialFooterElementPageUid = TEXT
            specialFooterElementPageUid {
                value = {$dbi.pids.specialFooterElement}

                #hide footer nav links on project pages (doktype=11)
                stdWrap.if {
                    isInList.field = doktype
                    value = 11
                    negate = 1
                }
            }

            marketPriceEstimationPid = TEXT
            marketPriceEstimationPid {
              value = {$dbi.pids.marketPriceEstimation}
            }

            brokerSearchPid = TEXT
            brokerSearchPid {
              value = {$dbi.pids.brokerSearch}
            }

            copyright = TEXT
            copyright {
                value = {$dbi.theme.copyright}
            }
        }
    }
}
