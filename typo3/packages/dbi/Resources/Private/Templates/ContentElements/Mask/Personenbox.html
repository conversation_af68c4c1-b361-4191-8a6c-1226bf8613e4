<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true">

<f:layout name="Default" />

<f:section name="Main">

  <f:variable name="borderClasses" value="border-t border-b" />

  <f:if condition="{data.tx_mask_border_theme} == 'top'">
    <f:variable name="borderClasses" value="border-t" />
  </f:if>
  <f:if condition="{data.tx_mask_border_theme} == 'bottom'">
    <f:variable name="borderClasses" value="border-b" />
  </f:if>

  <div class="group/personBox grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 {borderClasses} border-gray-300 py-6">
    <f:for each="{data.assets}" as="file">
       <dbic:image image="{file}" class="w-full" />
    </f:for>
    <div class="flex flex-col gap-6">
      <dbic:headline
        headline="{data.header}"
        layout="{data.header_layout}"
        overline="{data.subheader}"
        anchorId="{data.anchor_id}"
        class="gap-2"
      />
      <f:if condition="{data.bodytext}">
        {data.bodytext -> f:format.html()}
      </f:if>
      <f:if condition="{data.tx_mask_accordions}">
        <f:for each="{data.tx_mask_accordions}" as="data_item">
            <f:cObject typoscriptObjectPath="lib.tx_mask.content">{data_item.uid}</f:cObject>
        </f:for>
      </f:if>
    </div>
  </div>
</f:section>

</html>
