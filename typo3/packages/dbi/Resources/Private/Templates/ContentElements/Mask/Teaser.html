<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true">

<f:layout name="Default" />

<f:section name="Main">
  <f:variable name="snippetClass" value="px-6 py-8" />
  <f:if condition="{data.tx_mask_theme}=='base'">
    <f:variable name="snippetClass" value="" />
  </f:if>
  <f:if condition="{data.tx_mask_theme}=='white'">
    <f:variable name="snippetClass" value="" />
  </f:if>
  <dbic:teaser
    type="{data.tx_mask_orientation}"
    theme="{data.tx_mask_theme}"
    image="{data.assets.0}"
  >
    <dbic:content.snippet
      orientation="horizontal"
      header="{
        headline: data.header,
        layout: data.header_layout,
        subheader: data.subheader,
        anchorId: data.anchor_id
      }"
      buttons="{data.tx_mask_buttons}"
      theme="{data.tx_mask_theme}"
      class="{snippetClass}"
    >
      <div class="prose-a:underline prose-ul:list-disc prose-ol:list-decimal">
        {data.bodytext -> f:format.html()}
      </div>
    </dbic:content.snippet>
  </dbic:teaser>
</f:section>

</html>
