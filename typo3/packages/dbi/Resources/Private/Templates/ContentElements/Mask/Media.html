<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true">

  <f:layout name="Default" />

  <f:section name="Main">
    <f:if condition="{data.tx_mask_overline}">
      <f:then>
        <dbic:headline
          headline="{data.header}"
          layout="{data.header_layout}"
          overline="{data.subheader}"
          anchorId="{data.anchor_id}"
          />
      </f:then>
      <f:else>
        <dbic:headline
          headline="{data.header}"
          layout="{data.header_layout}"
          subline="{data.subheader}"
          anchorId="{data.anchor_id}"
          />
      </f:else>
    </f:if>

    <f:variable name="wrapperClass" value="" />
    <f:if condition="{data.tx_mask_orientation}=='col'">
      <f:variable name="wrapperClass" value="" />
    </f:if>

    <div class="flex flex-col gap-6">
      <f:for each="{data.assets}" as="asset">
        <div class="flex flex-col gap-5">
          <f:if condition="{asset.type}=='4'">
            <f:then>
              <f:if condition="{asset.extension}=='youtube'">
                <f:then>
                  <div class="p-6 border-dotted border-brand-500">Youtube Video (unhandled)</div>
                </f:then>
                <f:else>
                  <f:media file="{asset}" class="aspect-video" />
                </f:else>
              </f:if>
            </f:then>
            <f:else>
              <dbic:image image="{asset}" />
            </f:else>
          </f:if>
          <f:if condition="{asset.title} OR {asset.description}">
            <p class="text-sm">
              <f:if condition="{asset.title}">
                <strong class="font-medium text-brand-500">{asset.title}</strong><br />
              </f:if>
              {asset.description}
            </p>
          </f:if>
        </div>
      </f:for>
    </div>
  </f:section>
</html>
