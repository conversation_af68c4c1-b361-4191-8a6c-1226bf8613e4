<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <f:layout name="Default" />

  <f:section name="Main">
    <f:variable name="theme" value="{data.tx_dbi_container_background_color}" />
    <f:variable name="modeClass" value="" />
    <f:variable name="align" value="{data.tx_dbi_container_header_align}" />
    <f:variable name="alignClass" value="text-center" />

    <f:if condition="{theme} == 'brand'">
      <f:variable name="modeClass" value="dark" />
    </f:if>


    <f:if condition="{align} == 'left'">
        <f:variable name="alignClass" value="text-left" />
    </f:if>


    <dbic:container
      inset="{data.tx_dbi_container_inset}"
      margin="{data.tx_dbi_container_margin}"
      background="{data.tx_dbi_container_background_color}"
      intoPrevContainer="{data.tx_dbi_container_into_prev_container}"
      navTitle="{data.tx_dbi_container_nav_title}"
      cta="{data.tx_dbi_container_cta}"
      columns="{
        0: children_101
      }"
    >
      <a name="c{data.uid}" class="group-[.gap-6]/column:-my-3"></a>
      <f:if condition="{data.header}">
        <dbic:headline
          headline="{data.header}"
          layout="{data.header_layout}"
          subline="{data.subheader}"
          anchorId="{data.anchor_id}"
          class="mb-8 md:mb-6 lg:mb-10 {modeClass} {alignClass}"
        />
      </f:if>
    </dbic:container>
  </f:section>
</html>
