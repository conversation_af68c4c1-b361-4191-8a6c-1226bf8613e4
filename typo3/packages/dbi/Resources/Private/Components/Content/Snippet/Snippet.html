<html
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="header" type="Array" />

    <fc:param name="theme" type="string" optional="1" default="base" />

    <fc:param name="icon" type="string" optional="1" />

    <fc:param name="buttons" type="array" optional="1" />

    <fc:param name="simplified" type="string" optional="1" default="0" />


    <fc:param
      name="orientation"
      type="string"
      optional="1"
      default="vertical"
    />

    <!-- vertical|horizontal -->

    <f:variable name="orientationClass" value="flex-col gap-4 items-center" />

    <f:variable name="linksClass" value="items-center flex-col" />

    <f:if condition="{orientation} !== 'vertical'">
      <f:variable name="orientationClass" value="flex-row gap-6" />

      <f:variable name="linksClass" value="items-start flex-wrap" />
    </f:if>

    <f:if condition="{simplified}">
        <f:variable name="orientationClass" value="{orientationClass} items-start" />
    </f:if>

    <f:variable name="modeClass" value="" />
    <fc:renderer>
      <f:switch expression="{theme}">
        <f:case value="brand">
          <f:variable name="backgroundClasses" value="bg-brand-700 dark p-6" />

          <f:variable name="modeClass" value="dark" />
        </f:case>

        <f:case value="base-dark">
          <f:variable name="backgroundClasses" value="dark" />

          <f:variable name="modeClass" value="dark" />
        </f:case>

        <f:case value="light-brand">
          <f:variable name="backgroundClasses" value="bg-brand-50 p-6" />
        </f:case>

        <f:case value="light-gray">
          <f:variable name="backgroundClasses" value="bg-gray-50 p-6" />
        </f:case>

        <f:case value="white">
          <f:variable name="backgroundClasses" value="bg-white p-6" />
        </f:case>

        <f:defaultCase>
          <f:variable name="backgroundClasses" value="" />
        </f:defaultCase>
      </f:switch>

      <f:if condition="{simplified}">
        <f:variable name="backgroundClasses" value="bg-brand-950 dark" />
      </f:if>

      <div
        class="group/snippet w-full flex text-base {orientationClass} {class} {backgroundClasses}"
      >
        <f:if condition="{icon}">
          <dbic:icon
            name="{icon}"
            class="w-9 h-9 shrink-0 text-brand-950 group-[.dark]/snippet:text-white"
          />
        </f:if>

        <section
          class="{f:if(condition: '{orientation} == vertical', then: 'text-center', else: 'justify-center')} flex flex-col gap-4 text-brand-950 group-[.dark]/snippet:text-white"
        >
        <f:if condition="{simplified}">
            <f:then>
                <f:link.typolink
                    parameter="{buttons.0.tx_mask_link}"
                    title="{header.headline}"
                    target="_self"
                    additionalAttributes="{additionalAttributes}"
                    class="group/button button {buttonPadding} {themeClass} {link.class} {class}"
                    >
                    <dbic:headline
                        headline="{header.headline}"
                        layout="{header.layout}"
                        anchorId="{header.anchorId}"
                        class="{modeClass}"
                    />
                </f:link.typolink>
            </f:then>
            <f:else>
                <dbic:headline
                  headline="{header.headline}"
                  layout="{header.layout}"
                  overline="{header.subheader}"
                  anchorId="{header.anchorId}"
                  class="{modeClass}"
                />
            </f:else>
        </f:if>

          <fc:slot />

          <f:if condition="{simplified} == 0">
              <section class="flex gap-4 {linksClass}">
                <f:if condition="{buttons}">
                  <f:for each="{buttons}" as="button" iteration="i">
                    <f:if condition="{modeClass} == 'dark'">
                      <v:variable.set
                        name="button.tx_mask_theme"
                        value="{button.tx_mask_theme}-{modeClass}"
                      />
                    </f:if>
                    <f:if condition="{button.header}">
                      <f:then>
                        <dbic:button
                          link="{button.tx_mask_link}"
                          theme="{button.tx_mask_theme}"
                          icon="{button.tx_mask_icon}"

                        >
                          {button.header}
                        </dbic:button>
                      </f:then>

                      <f:else>
                        <dbic:button
                          link="{button.tx_mask_link}"
                          theme="{button.tx_mask_theme}"
                          icon="{button.tx_mask_icon}"
                        />
                      </f:else>
                    </f:if>
                  </f:for>
                </f:if>
              </section>
          </f:if>
        </section>
      </div>
    </fc:renderer>
  </fc:component>
</html>
