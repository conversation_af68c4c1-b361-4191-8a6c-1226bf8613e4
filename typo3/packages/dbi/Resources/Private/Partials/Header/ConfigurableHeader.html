<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:section name="Main">
    <header class="page-width" role="banner">
        <div class="hidden lg:block page-padding z-20 relative bg-brand-950">
            <div class="container">
                <f:render partial="Navigation/Support" section="Header" arguments="{_all}" />
                <div class="absolute top-0 left-0 bottom-0 right-0 -z-10 bg-brand-950 dark"></div>
            </div>
        </div>
        <div class="page-padding z-20 relative">
            <div class="px-4 lg:container bleeding-content flex gap-3 lg:gap-10 pb-6 pt-6 lg:pb-0 text-white bg-brand-700">
                    <f:link.typolink parameter="1" class="flex text-white" title="Zur Deutsche Bank Immobilien Startseite" additionalAttributes="{'aria-label': 'Deutsche Bank Logo - zur Startseite'}">
                        <dbic:logo hide-wordmark="1" class="w-8 h-9 sm:w-10 sm:h-11 lg:w-[75px] lg:h-[75px]" />
                    </f:link.typolink>
                <div class="flex flex-col w-full" x-data="navMenu('mobile-nav')">
                    <div class="flex w-full lg:justify-between">
                            <f:link.typolink parameter="1" class=" text-white leading-none inline-flex font-bold">
                                <dbic:logo hide-logomark="1" class="text-[18px] sm:text-[22px] lg:text-[18px]"/>
                            </f:link.typolink>

                        <!-- Jump Links Navigation -->
                        <div class="hidden lg:flex items-center gap-4 ml-auto">
                            <f:if condition="{headerLinks}">
                                <nav class="flex items-center gap-2">
                                    <f:for each="{headerLinks}" as="link">
                                        <a href="#{link.anchor}"
                                           class="px-3 py-2 text-white hover:text-brand-200 transition-colors duration-200 text-sm font-medium"
                                           data-smooth-scroll="true">
                                            {link.title}
                                        </a>
                                    </f:for>
                                </nav>
                            </f:if>

                            <!-- Fallback to default buttons if no jump links configured -->
                            <f:if condition="{headerLinks} == ''">
                                <f:if condition="{marketPriceEstimationPid}">
                                    <dbic:button
                                        buttonSize="no-padding"
                                        link="{f:uri.typolink(parameter: marketPriceEstimationPid)}"
                                        theme="primary"
                                        class="flex w-24 xs:w-auto ml-auto lg:gap-2 items-center px-3 lg:px-4 lg:py-2.5"
                                    >
                                        <dbic:icon name="sold-immobilie" class="flex"/>
                                        <span class="truncate">Immobilie einschätzen</span>
                                    </dbic:button>
                                </f:if>
                            </f:if>
                        </div>

                        <!-- Mobile Jump Links (Hamburger Menu) -->
                        <div class="flex lg:hidden items-center gap-3 ml-auto">
                            <f:if condition="{headerLinks}">
                                <!-- Mobile menu button for jump links -->
                                <button type="button"
                                        class="p-2 text-white hover:text-brand-200 transition-colors"
                                        x-data="{ open: false }"
                                        @click="open = !open"
                                        aria-label="Navigation menu">
                                    <dbic:icon name="menu" class="w-6 h-6"/>
                                </button>

                                <!-- Mobile dropdown menu -->
                                <div class="absolute top-full left-0 right-0 bg-brand-700 shadow-lg z-50"
                                     x-show="open"
                                     x-transition
                                     @click.away="open = false">
                                    <nav class="py-2">
                                        <f:for each="{headerLinks}" as="link">
                                            <a href="#{link.anchor}"
                                               class="block px-4 py-3 text-white hover:bg-brand-600 transition-colors"
                                               data-smooth-scroll="true"
                                               @click="open = false">
                                                {link.title}
                                            </a>
                                        </f:for>
                                    </nav>
                                </div>
                            </f:if>

                            <!-- Fallback mobile buttons -->
                            <f:if condition="{headerLinks} == ''">
                                <f:if condition="{brokerSearchPid}">
                                  <dbic:button
                                    buttonSize="no-padding"
                                    link="{f:uri.typolink(parameter: brokerSearchPid)}"
                                    theme="mobile-header-button"
                                    class="flex"
                                  >
                                    <span class="sr-only">Makler suchen</span>
                                    <dbic:icon name="share" class="w-10 h-10"/>
                                  </dbic:button>
                                </f:if>
                                <f:if condition="{marketPriceEstimationPid}">
                                    <dbic:button
                                        buttonSize="no-padding"
                                        link="{f:uri.typolink(parameter: marketPriceEstimationPid)}"
                                        theme="mobile-header-button"
                                        class="flex"
                                    >
                                      <span class="sr-only">Immobilie einschätzen</span>
                                      <dbic:icon name="sold-immobilie" class="w-10 h-10"/>
                                    </dbic:button>
                                </f:if>
                            </f:if>
                        </div>

                        <f:render partial="Navigation/Mobile" section="Main" arguments="{_all}" />
                    </div>
                    <f:render partial="Navigation/Main" section="Main" arguments="{_all}" />
                </div>
                <div class="absolute top-0 left-0 bottom-0 right-0 -z-10 bg-brand-700 dark"></div>
            </div>
        </div>
    </header>
  </f:section>
</html>
