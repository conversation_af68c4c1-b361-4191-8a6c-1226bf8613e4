<?php

declare(strict_types=1);

namespace Mogic\Dbi\Backend;

use TYPO3\CMS\Backend\Form\Element\AbstractFormElement;

/**
 * Information display for Header Links configuration
 */
class HeaderLinksInfo extends AbstractFormElement
{
    /**
     * Render information about header links functionality
     */
    public function render(): array
    {
        $html = '
            <div class="alert alert-info">
                <h4>Header Jump Links</h4>
                <p>This page template allows you to create jump links in the header that scroll to container sections on the page.</p>
                <ul>
                    <li><strong>Automatic Detection:</strong> All container components with headers on this page will be listed below</li>
                    <li><strong>Selection:</strong> Check the containers you want to include in the header navigation</li>
                    <li><strong>Jump Links:</strong> Selected headers will appear as clickable links in the page header</li>
                    <li><strong>Smooth Scrolling:</strong> Clicking a link will smoothly scroll to that section</li>
                </ul>
                <p><em>Note: Only container components (sections) with header text will be available for selection.</em></p>
            </div>
        ';

        return [
            'html' => $html
        ];
    }
}
