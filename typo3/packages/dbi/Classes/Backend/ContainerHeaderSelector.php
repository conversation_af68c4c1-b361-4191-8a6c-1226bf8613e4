<?php

declare(strict_types=1);

namespace Mogic\Dbi\Backend;

use TYPO3\CMS\Backend\Form\Element\AbstractFormElement;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Custom form element for selecting container headers
 */
class ContainerHeaderSelector extends AbstractFormElement
{
    /**
     * Render the container header selector
     */
    public function render(): array
    {
        $pageId = $this->getPageId();
        $currentValue = $this->getCurrentValue();

        $containers = $this->getContainersFromPage($pageId);

        if (empty($containers)) {
            $html = '
                <div class="alert alert-warning">
                    <strong>No containers found</strong><br>
                    Add container components with headers to this page to create jump links.
                </div>
            ';
        } else {
            $html = $this->renderContainerCheckboxes($containers, $currentValue);
        }

        return [
            'html' => $html
        ];
    }

    /**
     * Get the current page ID
     */
    private function getPageId(): int
    {
        // Try to get page ID from various sources
        $pageId = 0;

        if (isset($this->data['databaseRow']['pid'])) {
            $pageId = (int)$this->data['databaseRow']['pid'];
        } elseif (isset($this->data['databaseRow']['uid'])) {
            $pageId = (int)$this->data['databaseRow']['uid'];
        } elseif (isset($_GET['id'])) {
            $pageId = (int)$_GET['id'];
        }

        return $pageId;
    }

    /**
     * Get current selected value
     */
    private function getCurrentValue(): array
    {
        $value = $this->data['parameterArray']['itemFormElValue'] ?? '';

        if (empty($value)) {
            return [];
        }

        return GeneralUtility::trimExplode(',', $value, true);
    }

    /**
     * Get all container components from the page
     */
    private function getContainersFromPage(int $pageId): array
    {
        if ($pageId <= 0) {
            return [];
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('uid', 'header', 'CType', 'sorting')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->like('CType', $queryBuilder->createNamedParameter('tx_dbi_container_%')),
                $queryBuilder->expr()->neq('header', $queryBuilder->createNamedParameter(''))
            )
            ->orderBy('sorting')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Render checkboxes for container selection
     */
    private function renderContainerCheckboxes(array $containers, array $selectedValues): string
    {
        $fieldName = $this->data['parameterArray']['itemFormElName'];

        $html = '<div class="form-group">';
        $html .= '<div class="checkbox-list">';

        foreach ($containers as $container) {
            $uid = $container['uid'];
            $header = htmlspecialchars($container['header']);
            $ctype = $container['CType'];
            $checked = in_array((string)$uid, $selectedValues) ? 'checked="checked"' : '';

            $html .= '<div class="checkbox">';
            $html .= '<label>';
            $html .= '<input type="checkbox" name="' . $fieldName . '[]" value="' . $uid . '" ' . $checked . '>';
            $html .= '<strong>' . $header . '</strong>';
            $html .= '<small class="text-muted"> (' . $ctype . ', UID: ' . $uid . ')</small>';
            $html .= '</label>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        // Add JavaScript to handle the checkbox values
        $html .= '
            <script>
                (function() {
                    const checkboxes = document.querySelectorAll(\'input[name="' . $fieldName . '[]"]\');
                    const hiddenField = document.querySelector(\'input[name="' . $fieldName . '"]\');

                    if (!hiddenField) {
                        const hidden = document.createElement("input");
                        hidden.type = "hidden";
                        hidden.name = "' . $fieldName . '";
                        hidden.value = "' . implode(',', $selectedValues) . '";
                        checkboxes[0].parentNode.appendChild(hidden);
                    }

                    function updateHiddenField() {
                        const values = [];
                        checkboxes.forEach(function(cb) {
                            if (cb.checked) {
                                values.push(cb.value);
                            }
                        });
                        const hiddenField = document.querySelector(\'input[name="' . $fieldName . '"]\');
                        if (hiddenField) {
                            hiddenField.value = values.join(",");
                        }
                    }

                    checkboxes.forEach(function(cb) {
                        cb.addEventListener("change", updateHiddenField);
                    });
                })();
            </script>
        ';

        return $html;
    }
}
