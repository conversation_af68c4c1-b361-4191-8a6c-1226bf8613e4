<?php

declare(strict_types=1);

namespace Mogic\Dbi\DataProcessing;

use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for header links from FlexForm
 */
class HeaderLinksProcessor implements DataProcessorInterface
{
    /**
     * Process data for header links
     *
     * @param ContentObjectRenderer $cObj The data of the content element or page
     * @param array $contentObjectConfiguration The configuration of Content Object
     * @param array $processorConfiguration The configuration of this processor
     * @param array $processedData Key/value store of processed data (e.g. to be passed to a Fluid View)
     * @return array the processed data as key/value store
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        $targetVariableName = $cObj->stdWrapValue('as', $processorConfiguration, 'headerLinks');
        
        // Get FlexForm data
        $flexFormData = $cObj->stdWrapValue('flexFormData', $processorConfiguration, '');
        
        if (empty($flexFormData)) {
            $processedData[$targetVariableName] = [];
            return $processedData;
        }

        // Parse FlexForm XML
        $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
        $flexFormArray = $flexFormService->convertFlexFormContentToArray($flexFormData);
        
        $headerLinks = [];
        
        if (isset($flexFormArray['header_links']) && is_array($flexFormArray['header_links'])) {
            foreach ($flexFormArray['header_links'] as $linkData) {
                if (isset($linkData['link_item']) && is_array($linkData['link_item'])) {
                    $linkItem = $linkData['link_item'];
                    
                    // Only add links that have both title and link
                    if (!empty($linkItem['title']) && !empty($linkItem['link'])) {
                        $headerLinks[] = [
                            'title' => $linkItem['title'],
                            'link' => $linkItem['link'],
                            'icon' => $linkItem['icon'] ?? ''
                        ];
                    }
                }
            }
        }
        
        $processedData[$targetVariableName] = $headerLinks;
        
        return $processedData;
    }
}
