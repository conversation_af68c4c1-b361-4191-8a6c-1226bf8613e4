<?php

declare(strict_types=1);

namespace Mogic\Dbi\DataProcessing;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for header jump links from selected containers
 */
class HeaderLinksProcessor implements DataProcessorInterface
{
    /**
     * Process data for header jump links
     *
     * @param ContentObjectRenderer $cObj The data of the content element or page
     * @param array $contentObjectConfiguration The configuration of Content Object
     * @param array $processorConfiguration The configuration of this processor
     * @param array $processedData Key/value store of processed data (e.g. to be passed to a Fluid View)
     * @return array the processed data as key/value store
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        $targetVariableName = $cObj->stdWrapValue('as', $processorConfiguration, 'headerLinks');

        // Get FlexForm data
        $flexFormData = $cObj->stdWrapValue('flexFormData', $processorConfiguration, '');

        if (empty($flexFormData)) {
            $processedData[$targetVariableName] = [];
            return $processedData;
        }

        // Parse FlexForm XML
        $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
        $flexFormArray = $flexFormService->convertFlexFormContentToArray($flexFormData);

        $headerLinks = [];

        // Get selected container UIDs
        $selectedContainers = $flexFormArray['selected_containers'] ?? '';

        if (!empty($selectedContainers)) {
            $containerUids = GeneralUtility::trimExplode(',', $selectedContainers, true);
            $headerLinks = $this->getContainerHeaders($containerUids);
        }

        $processedData[$targetVariableName] = $headerLinks;

        return $processedData;
    }

    /**
     * Get container headers for the selected UIDs
     */
    private function getContainerHeaders(array $containerUids): array
    {
        if (empty($containerUids)) {
            return [];
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('uid', 'header', 'sorting')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->in('uid', $containerUids),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->neq('header', $queryBuilder->createNamedParameter(''))
            )
            ->orderBy('sorting')
            ->executeQuery();

        $containers = $result->fetchAllAssociative();
        $headerLinks = [];

        foreach ($containers as $container) {
            $headerLinks[] = [
                'title' => $container['header'],
                'anchor' => $this->generateAnchorId($container['header']),
                'uid' => $container['uid']
            ];
        }

        return $headerLinks;
    }

    /**
     * Generate a URL-safe anchor ID from header text
     */
    private function generateAnchorId(string $header): string
    {
        // Convert to lowercase and replace special characters
        $anchor = strtolower($header);
        $anchor = preg_replace('/[^a-z0-9\s-]/', '', $anchor);
        $anchor = preg_replace('/[\s-]+/', '-', $anchor);
        $anchor = trim($anchor, '-');

        return $anchor ?: 'section';
    }
}
