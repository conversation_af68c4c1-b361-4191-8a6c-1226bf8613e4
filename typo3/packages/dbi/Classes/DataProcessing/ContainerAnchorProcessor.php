<?php

declare(strict_types=1);

namespace Mogic\Dbi\DataProcessing;

use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor that adds anchor IDs to container content elements
 */
class ContainerAnchorProcessor implements DataProcessorInterface
{
    /**
     * Process data to add anchor IDs to containers
     *
     * @param ContentObjectRenderer $cObj The data of the content element or page
     * @param array $contentObjectConfiguration The configuration of Content Object
     * @param array $processorConfiguration The configuration of this processor
     * @param array $processedData Key/value store of processed data (e.g. to be passed to a Fluid View)
     * @return array the processed data as key/value store
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get the content elements
        $contentVariableName = $cObj->stdWrapValue('contentVariable', $processorConfiguration, 'mainContent');
        $content = $processedData[$contentVariableName] ?? [];
        
        if (empty($content)) {
            return $processedData;
        }

        // Get FlexForm data to see which containers should have anchors
        $pageData = $processedData['data'] ?? [];
        $flexFormData = $pageData['tx_dbi_header_links'] ?? '';
        
        $selectedContainerUids = [];
        if (!empty($flexFormData)) {
            $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
            $flexFormArray = $flexFormService->convertFlexFormContentToArray($flexFormData);
            $selectedContainers = $flexFormArray['selected_containers'] ?? '';
            
            if (!empty($selectedContainers)) {
                $selectedContainerUids = GeneralUtility::trimExplode(',', $selectedContainers, true);
            }
        }

        // Add anchor IDs to selected containers
        foreach ($content as &$contentElement) {
            $uid = $contentElement['data']['uid'] ?? 0;
            $ctype = $contentElement['data']['CType'] ?? '';
            $header = $contentElement['data']['header'] ?? '';
            
            // Check if this is a selected container
            if (in_array((string)$uid, $selectedContainerUids) && 
                strpos($ctype, 'tx_dbi_container_') === 0 && 
                !empty($header)) {
                
                // Generate anchor ID
                $anchorId = $this->generateAnchorId($header);
                $contentElement['data']['anchor_id'] = $anchorId;
            }
        }
        
        $processedData[$contentVariableName] = $content;
        
        return $processedData;
    }

    /**
     * Generate a URL-safe anchor ID from header text
     */
    private function generateAnchorId(string $header): string
    {
        // Convert to lowercase and replace special characters
        $anchor = strtolower($header);
        $anchor = preg_replace('/[^a-z0-9\s-]/', '', $anchor);
        $anchor = preg_replace('/[\s-]+/', '-', $anchor);
        $anchor = trim($anchor, '-');
        
        return $anchor ?: 'section';
    }
}
